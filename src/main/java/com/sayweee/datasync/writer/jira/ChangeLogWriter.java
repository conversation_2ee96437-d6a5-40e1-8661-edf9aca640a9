package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.model.entity.ChangeLogEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

@Component
@Service
@RequiredArgsConstructor
public class ChangeLogWriter {
    public void write(List<ChangeLogEntity> changeLogEntities) {
    }
}
